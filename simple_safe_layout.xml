<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:id="@id/fw_root" android:background="#00000000" android:padding="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <!-- 保持原有的img_icon，只增大尺寸 -->
        <ImageView android:textSize="24.0sp" android:gravity="center_vertical" android:layout_gravity="center" android:id="@id/img_icon" android:background="@drawable/circular_float_ball" android:padding="8.0dip" android:layout_width="80.0dip" android:layout_height="80.0dip" android:layout_marginBottom="4.0dip" android:src="@drawable/image_qnet" android:scaleType="centerInside" />
        
        <!-- 保持原有的img_start，只增大尺寸 -->
        <ImageView android:layout_gravity="center" android:id="@id/img_start" android:background="@android:color/transparent" android:padding="4.0dip" android:layout_width="56.0dip" android:layout_height="56.0dip" android:src="@drawable/ic_play_arrow" />
    </LinearLayout>
</LinearLayout>
