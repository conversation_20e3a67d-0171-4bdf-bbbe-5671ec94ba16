<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical"
    android:id="@id/fw_root"
    android:background="@drawable/circular_float_window_inactive"
    android:layout_width="80dp"
    android:layout_height="80dp"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- Hidden ImageView to maintain compatibility with existing code -->
    <ImageView
        android:id="@id/img_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <ImageView
        android:id="@id/img_start"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

</LinearLayout>
