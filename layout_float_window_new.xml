<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:id="@id/fw_root" android:background="#00000000" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <!-- Single circular control -->
    <ImageView 
        android:id="@id/img_start"
        android:layout_width="80.0dip" 
        android:layout_height="80.0dip"
        android:background="@drawable/circular_float_ball"
        android:scaleType="center"
        android:clickable="true"
        android:focusable="true" />
    
    <!-- Hidden ImageView to maintain compatibility -->
    <ImageView
        android:id="@id/img_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />
        
</LinearLayout>
