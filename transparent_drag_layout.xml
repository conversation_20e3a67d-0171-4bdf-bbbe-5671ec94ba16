<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:id="@id/fw_root" android:background="#00000000" android:padding="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <!-- 透明的拖动区域 - img_icon负责拖动 -->
        <ImageView android:textSize="24.0sp" android:gravity="center_vertical" android:layout_gravity="center" android:id="@id/img_icon" android:background="@android:color/transparent" android:padding="4.0dip" android:layout_width="80.0dip" android:layout_height="80.0dip" android:layout_marginBottom="-80.0dip" android:src="@android:color/transparent" android:alpha="0.0" />
        
        <!-- 可见的圆球按钮 - img_start负责点击和显示 -->
        <ImageView android:layout_gravity="center" android:id="@id/img_start" android:background="@drawable/circular_float_ball" android:padding="8.0dip" android:layout_width="80.0dip" android:layout_height="80.0dip" android:src="@drawable/image_qnet" android:scaleType="centerInside" />
    </LinearLayout>
</LinearLayout>
