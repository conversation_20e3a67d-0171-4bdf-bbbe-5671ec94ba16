.class Lcom/tencent/qnet/ui/float_window/FWService$9;
.super Ljava/lang/Object;
.source "FWService.java"

# interfaces
.implements Landroid/view/View$OnTouchListener;

# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/tencent/qnet/ui/float_window/FWService;->init_events()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

# instance fields
.field final synthetic this$0:Lcom/tencent/qnet/ui/float_window/FWService;
.field private initialX:F
.field private initialY:F
.field private initialTouchX:F
.field private initialTouchY:F
.field private isDragging:Z

# direct methods
.method constructor <init>(Lcom/tencent/qnet/ui/float_window/FWService;)V
    .locals 1

    iput-object p1, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->isDragging:Z

    return-void
.end method

# virtual methods
.method public onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 8

    invoke-virtual {p2}, Landroid/view/MotionEvent;->getAction()I

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_3

    if-eq v0, v1, :cond_1

    const/4 v2, 0x2

    if-eq v0, v2, :cond_0

    goto :goto_0

    :cond_0
    # ACTION_MOVE
    invoke-virtual {p2}, Landroid/view/MotionEvent;->getRawX()F

    move-result v0

    iget v2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->initialTouchX:F

    sub-float/2addr v0, v2

    invoke-virtual {p2}, Landroid/view/MotionEvent;->getRawY()F

    move-result p2

    iget v2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->initialTouchY:F

    sub-float/2addr p2, v2

    # Check if movement is significant enough to be considered dragging
    mul-float v2, v0, v0

    mul-float v3, p2, p2

    add-float/2addr v2, v3

    const/high16 v3, 0x42480000    # 50.0f

    cmpl-float v2, v2, v3

    if-lez v2, :cond_2

    iput-boolean v1, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->isDragging:Z

    # Update window position
    iget-object v2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {v2}, Lcom/tencent/qnet/ui/float_window/FWService;->access$100(Lcom/tencent/qnet/ui/float_window/FWService;)Landroid/view/WindowManager$LayoutParams;

    move-result-object v2

    iget v3, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->initialX:F

    add-float/2addr v3, v0

    float-to-int v0, v3

    iput v0, v2, Landroid/view/WindowManager$LayoutParams;->x:I

    iget-object v0, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {v0}, Lcom/tencent/qnet/ui/float_window/FWService;->access$100(Lcom/tencent/qnet/ui/float_window/FWService;)Landroid/view/WindowManager$LayoutParams;

    move-result-object v0

    iget v2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->initialY:F

    add-float/2addr v2, p2

    float-to-int p2, v2

    iput p2, v0, Landroid/view/WindowManager$LayoutParams;->y:I

    # Update window
    iget-object p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {p2}, Lcom/tencent/qnet/ui/float_window/FWService;->access$200(Lcom/tencent/qnet/ui/float_window/FWService;)Landroid/view/WindowManager;

    move-result-object p2

    iget-object v0, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {v0}, Lcom/tencent/qnet/ui/float_window/FWService;->access$300(Lcom/tencent/qnet/ui/float_window/FWService;)Landroid/widget/LinearLayout;

    move-result-object v0

    iget-object v2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {v2}, Lcom/tencent/qnet/ui/float_window/FWService;->access$100(Lcom/tencent/qnet/ui/float_window/FWService;)Landroid/view/WindowManager$LayoutParams;

    move-result-object v2

    invoke-interface {p2, v0, v2}, Landroid/view/WindowManager;->updateViewLayout(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    goto :goto_0

    :cond_1
    # ACTION_UP
    iget-boolean p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->isDragging:Z

    if-nez p2, :cond_2

    # This was a click, not a drag - toggle network state
    iget-object p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {p2}, Lcom/tencent/qnet/ui/float_window/FWService;->access$400(Lcom/tencent/qnet/ui/float_window/FWService;)Z

    move-result p2

    xor-int/2addr p2, v1

    iget-object v0, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {v0, p2}, Lcom/tencent/qnet/ui/float_window/FWService;->access$402(Lcom/tencent/qnet/ui/float_window/FWService;Z)Z

    # Update UI and network state
    iget-object v0, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {v0, p2}, Lcom/tencent/qnet/ui/float_window/FWService;->access$500(Lcom/tencent/qnet/ui/float_window/FWService;Z)V

    sput-boolean p2, Lcom/tencent/qnet/core/LocalVpnService;->IsActive:Z

    :cond_2
    const/4 p2, 0x0

    iput-boolean p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->isDragging:Z

    goto :goto_0

    :cond_3
    # ACTION_DOWN
    invoke-virtual {p2}, Landroid/view/MotionEvent;->getRawX()F

    move-result v0

    iput v0, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->initialTouchX:F

    invoke-virtual {p2}, Landroid/view/MotionEvent;->getRawY()F

    move-result p2

    iput p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->initialTouchY:F

    iget-object p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {p2}, Lcom/tencent/qnet/ui/float_window/FWService;->access$100(Lcom/tencent/qnet/ui/float_window/FWService;)Landroid/view/WindowManager$LayoutParams;

    move-result-object p2

    iget p2, p2, Landroid/view/WindowManager$LayoutParams;->x:I

    int-to-float p2, p2

    iput p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->initialX:F

    iget-object p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {p2}, Lcom/tencent/qnet/ui/float_window/FWService;->access$100(Lcom/tencent/qnet/ui/float_window/FWService;)Landroid/view/WindowManager$LayoutParams;

    move-result-object p2

    iget p2, p2, Landroid/view/WindowManager$LayoutParams;->y:I

    int-to-float p2, p2

    iput p2, p0, Lcom/tencent/qnet/ui/float_window/FWService$9;->initialY:F

    :goto_0
    return v1
.end method
