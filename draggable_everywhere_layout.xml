<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/fw_root" android:background="#00000000" android:padding="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <!-- 底层：可见的圆球按钮 - img_start负责显示 -->
    <ImageView android:layout_gravity="center" android:id="@id/img_start" android:background="@drawable/circular_float_ball" android:padding="8.0dip" android:layout_width="80.0dip" android:layout_height="80.0dip" android:src="@drawable/image_qnet" android:scaleType="centerInside" />
    
    <!-- 顶层：透明拖动区域 - img_icon负责拖动和点击 -->
    <ImageView android:layout_gravity="center" android:id="@id/img_icon" android:background="@android:color/transparent" android:padding="8.0dip" android:layout_width="80.0dip" android:layout_height="80.0dip" android:src="@android:color/transparent" android:clickable="true" android:focusable="true" />
    
</FrameLayout>
