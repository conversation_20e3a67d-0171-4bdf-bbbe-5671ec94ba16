<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:id="@id/fw_root" android:background="#00000000" android:padding="4.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <!-- 主要控件 - img_icon负责显示、拖动和点击 -->
        <ImageView android:textSize="24.0sp" android:gravity="center_vertical" android:layout_gravity="center" android:id="@id/img_icon" android:background="@drawable/circular_float_ball" android:padding="8.0dip" android:layout_width="80.0dip" android:layout_height="80.0dip" android:layout_marginBottom="4.0dip" android:src="@drawable/image_qnet" android:scaleType="centerInside" />
        
        <!-- 隐藏的img_start -->
        <ImageView android:layout_gravity="center" android:id="@id/img_start" android:background="@android:color/transparent" android:padding="4.0dip" android:layout_width="0.0dip" android:layout_height="0.0dip" android:visibility="gone" android:src="@drawable/ic_play_arrow" />
    </LinearLayout>
</LinearLayout>
