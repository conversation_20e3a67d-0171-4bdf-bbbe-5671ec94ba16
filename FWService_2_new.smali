.class Lcom/tencent/qnet/ui/float_window/FWService$2;
.super Ljava/lang/Object;
.source "FWService.java"

# interfaces
.implements Landroid/view/View$OnClickListener;

# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/tencent/qnet/ui/float_window/FWService;->init_events()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

# instance fields
.field final synthetic this$0:Lcom/tencent/qnet/ui/float_window/FWService;

# direct methods
.method constructor <init>(Lcom/tencent/qnet/ui/float_window/FWService;)V
    .locals 0

    iput-object p1, p0, Lcom/tencent/qnet/ui/float_window/FWService$2;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 3

    # Toggle network state
    iget-object p1, p0, Lcom/tencent/qnet/ui/float_window/FWService$2;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {p1}, Lcom/tencent/qnet/ui/float_window/FWService;->access$400(Lcom/tencent/qnet/ui/float_window/FWService;)Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    invoke-static {p1, v0}, Lcom/tencent/qnet/ui/float_window/FWService;->access$402(Lcom/tencent/qnet/ui/float_window/FWService;Z)Z

    # Update UI and network state
    iget-object p1, p0, Lcom/tencent/qnet/ui/float_window/FWService$2;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    invoke-static {p1, v0}, Lcom/tencent/qnet/ui/float_window/FWService;->access$500(Lcom/tencent/qnet/ui/float_window/FWService;Z)V

    # Set LocalVpnService.IsActive
    sput-boolean v0, Lcom/tencent/qnet/core/LocalVpnService;->IsActive:Z

    # Show toast message
    iget-object p1, p0, Lcom/tencent/qnet/ui/float_window/FWService$2;->this$0:Lcom/tencent/qnet/ui/float_window/FWService;

    if-eqz v0, :cond_0

    const-string v1, "网络加速已开启"

    goto :goto_0

    :cond_0
    const-string v1, "网络加速已关闭"

    :goto_0
    const/4 v2, 0x0

    invoke-static {p1, v1, v2}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object p1

    invoke-virtual {p1}, Landroid/widget/Toast;->show()V

    return-void
.end method
